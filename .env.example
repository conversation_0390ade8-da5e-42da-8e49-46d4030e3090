# Azure OpenAI Configuration
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com/
AZURE_OPENAI_KEY=your-api-key-here
AZURE_OPENAI_MODEL=gpt-4o
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Alternative OpenAI Configuration (if not using Azure)
OPENAI_API_KEY=your-openai-api-key-here

# Streamlit Configuration
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=localhost

# Application Configuration
APP_NAME=Enterprise AI Pipeline Generator
APP_VERSION=1.0.0
DEBUG_MODE=false

# Database Configuration (for generated pipelines)
DATABASE_URL=postgresql://user:password@localhost:5432/pipeline_db
REDIS_URL=redis://localhost:6379

# Vector Database Configuration
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
QDRANT_URL=http://localhost:6333
WEAVIATE_URL=http://localhost:8080

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Security Configuration
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=enterprise_pipeline.log

# Docker Configuration
DOCKER_REGISTRY=your-registry.com
DOCKER_IMAGE_TAG=latest

# Kubernetes Configuration
KUBERNETES_NAMESPACE=default
KUBERNETES_CLUSTER=your-cluster-name
