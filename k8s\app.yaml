apiVersion: apps/v1
kind: Deployment
metadata:
  name: enterprise-pipeline
  namespace: enterprise-pipeline
spec:
  replicas: 3
  selector:
    matchLabels:
      app: enterprise-pipeline
  template:
    metadata:
      labels:
        app: enterprise-pipeline
    spec:
      containers:
      - name: pipeline
        image: enterprise-pipeline:latest
        ports:
        - containerPort: 8000
        env:
        - name: REDIS_URL
          value: "redis://redis-service:6379/0"
        - name: AZURE_OPENAI_ENDPOINT
          value: "https://admins.openai.azure.com/"
        - name: AZURE_OPENAI_KEY
          value: "********************************"
        - name: AZURE_OPENAI_MODEL
          value: "gpt-4o"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: enterprise-pipeline-service
  namespace: enterprise-pipeline
spec:
  selector:
    app: enterprise-pipeline
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: enterprise-pipeline-hpa
  namespace: enterprise-pipeline
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: enterprise-pipeline
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
