#!/usr/bin/env python3
"""
Setup script for Enterprise AI Pipeline Generator
Helps with initial configuration and environment setup
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is 3.11 or higher."""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'streamlit',
        'autogen-agentchat',
        'azure-openai',
        'python-dotenv',
        'docker',
        'kubernetes'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    return missing_packages

def install_dependencies():
    """Install required dependencies."""
    print("\n📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def setup_environment():
    """Set up environment configuration."""
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        print("\n⚙️ Setting up environment configuration...")
        shutil.copy(env_example, env_file)
        print("✅ Created .env file from .env.example")
        print("📝 Please edit .env file with your configuration:")
        print("   - Add your Azure OpenAI API key")
        print("   - Configure other settings as needed")
        return True
    else:
        print("❌ .env.example file not found")
        return False

def check_docker():
    """Check if Docker is available."""
    try:
        subprocess.check_output(['docker', '--version'], stderr=subprocess.STDOUT)
        print("✅ Docker is available")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ Docker is not available (optional for basic usage)")
        return False

def create_directories():
    """Create necessary directories."""
    directories = [
        'logs',
        'temp',
        'generated_pipelines'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def run_tests():
    """Run basic tests to verify setup."""
    print("\n🧪 Running basic tests...")
    
    # Test imports
    try:
        import streamlit
        import autogen
        print("✅ Core imports successful")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test environment file
    if not Path('.env').exists():
        print("⚠️ .env file not found - please configure environment")
        return False
    
    print("✅ Basic tests passed")
    return True

def main():
    """Main setup function."""
    print("🏢 Enterprise AI Pipeline Generator - Setup")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check and install dependencies
    missing = check_dependencies()
    if missing:
        print(f"\n📦 Missing packages: {', '.join(missing)}")
        if input("Install missing packages? (y/n): ").lower() == 'y':
            if not install_dependencies():
                sys.exit(1)
        else:
            print("❌ Cannot proceed without required packages")
            sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check Docker
    check_docker()
    
    # Create directories
    print("\n📁 Creating directories...")
    create_directories()
    
    # Run tests
    if not run_tests():
        print("⚠️ Some tests failed - please check configuration")
    
    print("\n🎉 Setup completed!")
    print("\n🚀 Next steps:")
    print("1. Edit .env file with your configuration")
    print("2. Run: streamlit run streamlit_pipeline_app.py")
    print("3. Open http://localhost:8501 in your browser")
    print("\n📚 For more information, see README.md")

if __name__ == "__main__":
    main()
